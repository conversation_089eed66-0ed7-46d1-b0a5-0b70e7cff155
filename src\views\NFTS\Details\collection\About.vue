<template>
    <div class="about-box">
        <div class="header">About Doodle #9752</div>
        <div class="flex gap-[4px] text-[12px]">By:The Doge Capital
            <img :src="certifyIcon" alt="logo" />
        </div>
        <div class="desc">
            A community-driven collectibles project featuring art by <PERSON><PERSON>. Doodles come in a joyful range of
            colors, traits and sizes with acollection size of 10,000. Each Doodle allows its owner to vote for
            experiences and activations paid for by the Doodles CommunityTreasury. <PERSON><PERSON> is the working allas for
            <PERSON>, a Canadian-based illustrator, designer, animator and muralist.
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import certifyIcon from '@/assets/NFTS/details/certify.svg'

</script>
<style lang="scss" scoped>
.about-box {
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    border-radius: 16px;
    background: var(--Neutral-50, #F9FAFB);

    .header {
        color: #000;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
    }

    .desc {
        color: #595959;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
    }
}
</style>