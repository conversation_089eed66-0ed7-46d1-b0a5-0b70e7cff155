import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './libs/iconfont/iconfont.css'
import './libs/iconfont/iconfont.js'
import 'element-plus/dist/index.css'
import './permission'
import AutoImportComponent from './utils/auto-import-component'
import ElementPlus from 'element-plus'
import element from './libs/element/index'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'
import i18n from './locales'
import './style/index.scss'
import ArcoVue from '@arco-design/web-vue';
import '@arco-design/web-vue/dist/arco.css';

const app = createApp(App)
let locale = '';

if(i18n.global.fallbackLocale.value == 'zh') {
    locale = zhCn
} else {
    locale = en
}

app.use(ElementPlus, {
  locale: locale,
})

app.use(ArcoVue);
app.use(createPinia())
app.use(router)
app.use(AutoImportComponent)

app.use(element)
app.use(i18n)
app.mount('#app')

