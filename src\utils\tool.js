// 计算传入日期间隔X天的日期
export const getBeforeDate = (num) =>{
    const now = new Date();
    const pastDate = new Date();
    pastDate.setDate(now.getDate() - num);

    const year = pastDate.getFullYear();
    const month = String(pastDate.getMonth() + 1).padStart(2, '0');
    const day = String(pastDate.getDate()).padStart(2, '0');

    const formattedPastDate = `${year}-${month}-${day}`;
    return formattedPastDate
}
// 计算当前日期和传入日期间隔天数
export const getDaysBetween = (date1) => {
    const d1 = new Date(date1);
    const d2 = new Date();

    const diffTime = Math.abs(d2 - d1); // 时间差（毫秒）
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // 转为天数

    return diffDays;
}
// 获取唯一key
export const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0
        const v = c === 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
    })
}
// 将对象key换成新key
export const renameKey = (obj, oldKey, newKey) => {
    if (obj.hasOwnProperty(oldKey)) {
        obj[newKey] = obj[oldKey]
        delete obj[oldKey]
    }
    return obj
}
// 时间戳转换为年月日-时分秒
export const formatTimestamp = (timestamp) => {
    const d = new Date(timestamp * 1000)
    const pad = n => n.toString().padStart(2, '0')

    const year = d.getFullYear()
    const month = pad(d.getMonth() + 1)
    const day = pad(d.getDate())
    const hour = pad(d.getHours())
    const minute = pad(d.getMinutes())
    const second = pad(d.getSeconds())

    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
// 获取接下来一周的日期
export const getNextWeekDates = () => {
    const dates = [];
    const today = new Date();

    // 获取未来一周的日期
    for (let i = 0; i < 7; i++) {
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + i);  // 设置未来的日期

        // 格式化日期为 "YYYY-MM-DD" 格式
        const year = futureDate.getFullYear();
        const month = String(futureDate.getMonth() + 1).padStart(2, '0');  // 月份从0开始，需要+1
        const day = String(futureDate.getDate()).padStart(2, '0');

        dates.push(`${year}-${month}-${day}`);
    }

    return dates;
}


export const  truncateToTwoDecimal = (num) => {
    if(!num) return 0
    return Math.floor(num * 100) / 100
}