<template>
  <el-container>
    <Header />
    <el-main class="h-[calc(100vh-112px)] overflow-auto pl-8 pr-8">
      <router-view />
    </el-main>
    <Footer />
    <el-footer class="h-[40px] bg-[var(--bg-6-96)] flex items-center justify-between border-t border-[var(--border-3)]">
      <div class="flex items-center gap-6">
        <div class="text-xs leading-[140%] text-[var(--text-5)] cursor-pointer hover:text-[var(--text-3)]">{{
          t('components.layout.terms-of-service') }}</div>
        <div class="text-xs leading-[140%] text-[var(--text-5)] cursor-pointer hover:text-[var(--text-3)]">{{
          t('components.layout.privacy-policy') }}</div>
      </div>
      <div class="flex items-center">

        <el-popover class="box-item" title="" width="124" placement="top">
          <div class="cursor-pointer" @click="onClickItem(1)">
            {{ locale === 'en' ? t('components.layout.ob523l') : 'English' }}
          </div>
          <template #reference>
            <div class="flex items-center mr-7 text-[var(--text-5)] hover:text-[var(--text-3)]">
              <i class="text-base iconfont icon-website mr-1"></i>
              <div class="text-xs cursor-pointer">{{ getLocaleText }}</div>
            </div>
          </template>
        </el-popover>
        <i
          class="w-6 h-6 text-base flex items-center justify-center iconfont icon-icon_issue mr-2 text-[var(--text-5)] hover:text-[var(--text-3)] hover:bg-[var(--bg-4)]"></i>
        <i
          class="w-6 h-6 text-base flex items-center justify-center iconfont icon-icon_issue mr-2 text-[var(--text-5)] hover:text-[var(--text-3)] hover:bg-[var(--bg-4)]"></i>
        <i
          class="w-6 h-6 text-base flex items-center justify-center iconfont icon-icon_theme mr-[28px] text-[var(--text-5)] hover:text-[var(--text-3)] hover:bg-[var(--bg-4)]"></i>
        <div class="flex items-center text-[var(--text-5)] hover:text-[var(--text-3)]">
          <i class="text-base iconfont icon-a-call-filled mr-1"></i>
          <div class="text-xs leading-[140%] cursor-pointer">Support</div>
        </div>
      </div>
    </el-footer>
  </el-container>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import Header from "./Header/index.vue"
import Footer from "@/components/Footer/index.vue"
import { useI18n } from 'vue-i18n';
const { locale, t } = useI18n();

const route = useRoute()
const router = useRouter()
const scrollTop = ref('')
const userStore = useUserStore()

// 底部菜单点击
const getLocaleText = computed(() => {
  return locale.value === 'en' ? 'English' : t('components.layout.ob523l')
});
const onClickItem = (type) => {
  switch (type) {
    case 1:
      locale.value = locale.value === 'en' ? 'zh' : 'en';
      break;

    default:
      break;
  }
};

</script>
<style lang='scss' scoped>
.el-container {
  background: url('@/assets/bg.svg');
  background-repeat: no-repeat;
  background-size: cover;
}
</style>