<svg width="1906" height="1080" viewBox="0 0 1906 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3482_11145)">
<rect width="1906" height="1080" fill="white"/>
<g opacity="0.2" filter="url(#filter0_f_3482_11145)">
<circle cx="1063.06" cy="0.59584" r="96.9362" fill="#6891EF"/>
</g>
<g opacity="0.2" filter="url(#filter1_f_3482_11145)">
<circle cx="909.106" cy="-37.8936" r="84.1064" fill="#28DD7C"/>
</g>
</g>
<defs>
<filter id="filter0_f_3482_11145" x="846.128" y="-216.34" width="433.872" height="433.872" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur_3482_11145"/>
</filter>
<filter id="filter1_f_3482_11145" x="705" y="-242" width="408.213" height="408.213" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur_3482_11145"/>
</filter>
<clipPath id="clip0_3482_11145">
<rect width="1906" height="1080" fill="white"/>
</clipPath>
</defs>
</svg>
