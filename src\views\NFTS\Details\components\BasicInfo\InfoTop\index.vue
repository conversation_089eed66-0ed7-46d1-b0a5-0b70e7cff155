<template>
  <div id="nfts-details-info-top" class="w-full">
    <div class="flex justify-between items-center w-full">
      <div class="flex items-center">
        <p
          class="text-[#000000] font-[700] mr-8px overflow-one-hidden max-w-[300px]"
        >
          Doodle #9752Doodle
        </p>
        <NameIconGroups
          v-if="showNameIconGroups"
          className="gap-[8px]"
          iconClass="text-[24px]"
        />
      </div>
      <ShareGroupButton class="flex-srink-0 gap-[20px]" />
    </div>
    <div className="mt-[18px] flex items-center" v-if="showSubInfo">
      <SubInfo />
      <NameIconGroups class="ml-[3px] gap-[3px]" />
    </div>
    <div class="w-full flex items-center justify-between mt-[20px]">
      <div class="flex items-center">
        <InfoMore v-model="showMore" className="rounded-[8px]" />
        <CoinIconsGap
          className="bg-[#F2F2F2] h-[24px] ml-[12px] rounded-[8px] px-[4px]"
        />
      </div>
      <BatchButtons className="mt-[6px] mb-[6px]" />
    </div>
    <MoreInfo
      className="mt-[16px] p-[12px] bg-[#F9FAFB] rounded-[16px]"
      v-show="showMore"
    />
    <CoinTabs class="mt-[40px]" />
  </div>
</template>
<script setup>
import NameIconGroups from "@/views/NFTS/Details/components/BasicInfo/nameIconGroups.vue";
import ShareGroupButton from "@/views/NFTS/Details/components/BasicInfo/shareGroupButton.vue";
import SubInfo from "@/views/NFTS/Details/components/BasicInfo/subInfo.vue";
import InfoMore from "@/views/NFTS/Details/components/BasicInfo/infoMore.vue";
import CoinIconsGap from "@/views/NFTS/Details/components/BasicInfo/coinIconsGap.vue";
import MoreInfo from "@/views/NFTS/Details/components/BasicInfo/moreInfo.vue";
import BatchButtons from "@/views/NFTS/Details/components/BasicInfo/batchButtons.vue";
import CoinTabs from "@/views/NFTS/Details/components/BasicInfo/coinTabs.vue";
defineProps({
  showNameIconGroups: {
    type: Boolean,
    default: true,
  },
  showSubInfo: {
    type: Boolean,
    default: true,
  },
});
const showMore = ref(false);
</script>
<style lang="scss" scoped>
#nfts-details-info-top {
  font-family: "SF Pro Display";
}
</style>
