<template>
    <footer class="flex justify-between px-8 py-10">
        <div class="flex flex-col justify-between">
            <div class="flex flex-col gap-6">
                <img class="w-[214px] h-[32px]" src="@/assets/logo.svg" alt="logo" />
                <p class="w-[458px] text-sm text-[--text-4] leading-[21px]">The largest NFT marketplace and Runes
                    platform. Buy, sell
                    and discover Ordinals and
                    NFTs across
                    Solana,
                    Bitcoin, Ethereum, Base, ApeChain, Abstract, Berachain, Monad Testnet, Avalanche, Arbitrum, Sei, BNB
                    Chain, and Polygon.</p>
            </div>
            <p class="text-xs leading-[18px] text-[--text-4]">© 2018 - 2025 Ozone Networks, Inc</p>
        </div>
        <div class="flex gap-[96px]">
            <div class="flex flex-col gap-2">
                <span class="text-[--text-3] font-semibold leading-6">Company</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">About</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Careers</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Privacy
                    policy</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Terms of
                    service</span>
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-[--text-3] font-semibold leading-6">Resources</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Support
                    center</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Community
                    standards</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Platform
                    status</span>
                <span
                    class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Partners</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Taxes</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Blog</span>
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-[--text-3] font-semibold leading-6">Community</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">X</span>
                <span
                    class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Instagram</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Threads</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Discord</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">YouTube</span>

            </div>
            <div class="flex flex-col gap-2">
                <span class="text-[--text-3] font-semibold leading-6">Marketplace</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Drops</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Stats</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Art</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Gaming</span>
                <span
                    class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Memberships</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">PFPS</span>
                <span
                    class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Photography</span>
                <span class="text-sm leading-5 text-[--text-4] cursor-pointer hover:text-[var(--text-1)]">Music</span>

            </div>
        </div>
    </footer>
</template>
<script setup>

</script>
<style lang="scss" scoped></style>