<template>
  <div class="flex items-center gap-[4px]" :class="className">
    <img
      v-for="item in coinList"
      :key="item"
      :src="`${coinIcons[item]}`"
      :class="iconClassName"
    />
  </div>
</template>
<script setup>
import ethereum from "@/assets/NFTS/details/ethereum_icon.svg";
import abstract from "@/assets/NFTS/details/abstract_icon.svg";
import berachain from "@/assets/NFTS/details/berachain_icon.svg";
import base from "@/assets/NFTS/details/base_icon.svg";
import more from "@/assets/NFTS/details/more_icon.svg";
import apeChain from "@/assets/NFTS/details/apeChain_icon.svg";
defineProps({
  className: {
    type: String,
    default: "",
  },
  coinList: {
    type: Array,
    default: () => ["ethereum", "abstract", "berachain", "base", "more"],
  },
  iconClassName: {
    type: String,
    default: "text-[16px]",
  },
});
const coinIcons = {
  ethereum,
  abstract,
  berachain,
  base,
  more,
  apeChain,
};
</script>
