<template>
  <div
    class="flex flex-grap items-center share-group-button"
    :class="className"
  >
    <BaseIcon icon="copy" class="text-[20px] cursor-pointer" />
    <BaseIcon icon="star" class="text-[20px] cursor-pointer" />
    <BaseIcon icon="sync" class="text-[20px] cursor-pointer" />
    <AnalysisButtons />
  </div>
</template>
<script setup>
import BaseIcon from "@/components/BaseIcon/index.vue";
import AnalysisButtons from "@/views/NFTS/Details/components/BasicInfo/analysisButtons.vue";
defineProps({
  className: String,
  info: {
    type: Object,
    default: () => ({}),
  },
});
</script>
