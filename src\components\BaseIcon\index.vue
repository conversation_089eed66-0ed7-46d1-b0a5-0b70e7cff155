<template>
    <svg class="icon svg-icon" aria-hidden="true" :style="{ fill: color }">
        <use :xlink:href="iconName"></use>
    </svg>
</template>

<script setup>
const props = defineProps({
    icon: {
        type: String,
        required: true,
    },
    color: {
        type: String,
        required: false,
    },
});
const iconName = computed(() => {
    return `#icon-${props.icon}`;
});
</script>

<style>
.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
</style>
