import i18n from '../locales/index.js';
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import router from '@/router'


const TOKEN_KEY = 'token'

export const useUserStore = defineStore('user', () => {
  const route = useRoute()

  const token = ref(localStorage[TOKEN_KEY])
  // const expires = ref(localStorage.expires)
  const userInfo = ref(JSON.parse(localStorage.userInfo || '{}'))
  const lang= ref(localStorage.duixian_lang || 'zh')

  // 判断是否登录
  const checkLogin = async (isGoLogin = true) => {
    token.value = localStorage[TOKEN_KEY]
    if (token.value) {
      return true
    } else {
      if (isGoLogin){
        logout(true)
      } 
      return false
    }
  }
  // 计算用户角色
  const roles = computed(() => {
    if (!token.value) {
      localStorage.uid = 0
      return 'tourist'; // 游客
    } else if (userInfo.value && userInfo.value.roles && userInfo.value.roles.length > 0) {
      return 'member'; // 会员用户
    } else {
      return 'normal'; // 普通用户
    }
  });


  const currentRole = computed(() => {
    if (!token.value) {
      localStorage.uid = 0
      return 'tourist'; // 游客
    } else if (userInfo.value && userInfo.value.roles && userInfo.value.roles.length && userInfo.value.roles.includes('dx_pro')) {
      return 'pro'; // 会员用户
    } else if(userInfo.value && userInfo.value.roles && userInfo.value.roles.length && userInfo.value.roles.includes('dx_light')) {
      return 'light'; // 普通用户
    }else{
      return 'tourist';
    }
  });

  // 登录成功
  const toLogin = async options => {
    token.value = 'TOKEN ' + '12312312'
    localStorage.uid = 1123
    localStorage.token = 1231456
    return new Promise((resolve, reject) => {
      resolve()
    })
    return
    token.value = 'TOKEN ' + options.accessToken
    userInfo.value = JSON.stringify(options)
    localStorage.userInfo = JSON.stringify(options)
    localStorage.uid = options.uid
    localStorage.plant_code = 'pc_web=' + options.plant_code
    localStorage[TOKEN_KEY] = token.value
    localStorage.roles = JSON.stringify(options.roles)
    localStorage.accessToken = options.accessToken
    localStorage.mac = ""
    ElMessage.success(i18n.global.t('stores.user.sf84fl'));
    setTimeout(() => {
      window.location.replace('/');  // 刷新页面并跳转到首页
    }, 1000);
  }


  // 退出
  const logout = () => {
    localStorage.clear()
    ElMessage.success('退出成功');
    userInfo.value = null
    token.value = null
    router.replace({
      path: '/',
    })
  }
  const logouts = () => {
    localStorage.clear()
    userInfo.value = null
    token.value = null
    router.replace({
      path: '/',
    })
  }

  const changeLanguage = (val) => {
    lang.value= val
  }
  
  return {
    lang,
    userInfo,
    roles,
    token,
    currentRole,
    checkLogin,
    toLogin,
    logout,
    logouts,
    changeLanguage
  }
})
