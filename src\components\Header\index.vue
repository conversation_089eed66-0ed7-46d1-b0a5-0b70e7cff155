<template>
    <el-header class="h-[72px] border flex items-center justify-between px-8 py-[26px] gap-6">
        <div class="flex items-center gap-8">
            <a href="/">
                <img :src="Logo" alt="logo" />
            </a>
            <el-input v-model="searchValue" class="custom-input" size="large" style="width: 300px"
                placeholder="Search TrendSea">
                <template #prefix>
                    <BaseIcon icon="search" class="w-[18px] h-[18px]" />
                </template>
                <template #suffix>
                    <div class="w-6 h-6 rounded bg-[--border-3] flex items-center justify-center text-black-80">/</div>
                </template>
            </el-input>
            <ul class="flex items-center gap-6">
                <li class="text-sm font-bold text-[var(--text-5)] cursor-pointer hover:text-[var(--text-1)] px-2"
                    v-for="item in menuList" :key="item.name">{{ item.name }}
                </li>
            </ul>
        </div>
        <div class="flex items-center cursor-pointer gap-2">
            <BaseIcon icon="wallet" class="w-[16px] h-[14px]" />
            <span class="text-sm font-bold text-[var(--text-7)] ml-1 mr-6">Connect Wallet</span>
            <BaseIcon icon="mine" class="w-[20px] h-[20px]" />
        </div>

    </el-header>
</template>
<script setup>
import Logo from '../../../public/logo.svg'
import BaseIcon from "@/components/BaseIcon/index.vue"

const searchValue = ref('')
const menuList = ref([
    { name: 'NFT', path: '/nft' },
    { name: 'Studio', path: '/studio' }
])
</script>
<style lang="scss" scoped>
:deep(.custom-input .el-input__wrapper) {
    border-radius: 8px;
    background-color: var(--border-1);
}
</style>