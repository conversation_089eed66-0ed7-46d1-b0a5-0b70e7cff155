@font-face {
  font-family: "iconfont"; /* Project id 5008432 */
  src: url('iconfont.woff2?t=1756719915050') format('woff2'),
       url('iconfont.woff?t=1756719915050') format('woff'),
       url('iconfont.ttf?t=1756719915050') format('truetype'),
       url('iconfont.svg?t=1756719915050#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-Global:before {
  content: "\e7a7";
}

.icon-link1:before {
  content: "\e7a5";
}

.icon-shopping_cart:before {
  content: "\e7a6";
}

.icon-a-NFToffer:before {
  content: "\e7a4";
}

.icon-a-Collectionoffer:before {
  content: "\e7a3";
}

.icon-a-Multi-chainUnique:before {
  content: "\e7a0";
}

.icon-a-Multi-chainCopy:before {
  content: "\e7a1";
}

.icon-qianbao:before {
  content: "\e7a2";
}

.icon-scan:before {
  content: "\e75d";
}

.icon-more:before {
  content: "\e75e";
}

.icon-history:before {
  content: "\e75f";
}

.icon-at:before {
  content: "\e761";
}

.icon-more-vertical:before {
  content: "\e762";
}

.icon-eye:before {
  content: "\e763";
}

.icon-Launch:before {
  content: "\e764";
}

.icon-customer-service:before {
  content: "\e765";
}

.icon-heart-fill:before {
  content: "\e766";
}

.icon-Export:before {
  content: "\e767";
}

.icon-code-block:before {
  content: "\e768";
}

.icon-code:before {
  content: "\e769";
}

.icon-code-square:before {
  content: "\e76a";
}

.icon-mute-fill:before {
  content: "\e76b";
}

.icon-backward:before {
  content: "\e76c";
}

.icon-play-arrow:before {
  content: "\e76d";
}

.icon-skip-next-fill:before {
  content: "\e76e";
}

.icon-record:before {
  content: "\e76f";
}

.icon-fullscreen:before {
  content: "\e770";
}

.icon-mute:before {
  content: "\e771";
}

.icon-sound:before {
  content: "\e772";
}

.icon-skip-next:before {
  content: "\e773";
}

.icon-music:before {
  content: "\e774";
}

.icon-play-arrow-fill:before {
  content: "\e775";
}

.icon-forward:before {
  content: "\e776";
}

.icon-skip-previous-fill:before {
  content: "\e777";
}

.icon-record-stop:before {
  content: "\e778";
}

.icon-sound-fill:before {
  content: "\e779";
}

.icon-pause-circle:before {
  content: "\e77a";
}

.icon-pause:before {
  content: "\e77b";
}

.icon-pause-circle-fill:before {
  content: "\e77c";
}

.icon-play-circle:before {
  content: "\e77d";
}

.icon-skip-previous:before {
  content: "\e77e";
}

.icon-fullscreen-exit:before {
  content: "\e77f";
}

.icon-exclamation-circle-fill:before {
  content: "\e782";
}

.icon-plus:before {
  content: "\e783";
}

.icon-question-circle:before {
  content: "\e784";
}

.icon-plus-circle:before {
  content: "\e785";
}

.icon-close-circle-fill:before {
  content: "\e786";
}

.icon-check-circle:before {
  content: "\e787";
}

.icon-check-square:before {
  content: "\e788";
}

.icon-exclamation:before {
  content: "\e789";
}

.icon-clock-circle:before {
  content: "\e78a";
}

.icon-info-circle-fill:before {
  content: "\e78b";
}

.icon-minus:before {
  content: "\e78c";
}

.icon-close-circle:before {
  content: "\e78d";
}

.icon-check:before {
  content: "\e78e";
}

.icon-info:before {
  content: "\e78f";
}

.icon-question-circle-fill:before {
  content: "\e790";
}

.icon-minus-circle-fill:before {
  content: "\e791";
}

.icon-close:before {
  content: "\e792";
}

.icon-exclamation-polygon-fill:before {
  content: "\e793";
}

.icon-check-circle-fill:before {
  content: "\e794";
}

.icon-question:before {
  content: "\e795";
}

.icon-info-circle:before {
  content: "\e797";
}

.icon-minus-circle:before {
  content: "\e798";
}

.icon-stop:before {
  content: "\e799";
}

.icon-exclamation-circle:before {
  content: "\e79a";
}

.icon-plus-circle-fill:before {
  content: "\e781";
}

.icon-play-circle1:before {
  content: "\e760";
}

.icon-heart:before {
  content: "\e796";
}

.icon-storage:before {
  content: "\e79b";
}

.icon-mosaic:before {
  content: "\e79c";
}

.icon-face-frown-fill:before {
  content: "\e79d";
}

.icon-home:before {
  content: "\e757";
}

.icon-save:before {
  content: "\e745";
}

.icon-eye-invisible:before {
  content: "\e750";
}

.icon-send:before {
  content: "\e753";
}

.icon-face-smile-fill:before {
  content: "\e6e1";
}

.icon-archive:before {
  content: "\e6ee";
}

.icon-face-meh-fill:before {
  content: "\e6f1";
}

.icon-gift:before {
  content: "\e6d5";
}

.icon-branch:before {
  content: "\e6e3";
}

.icon-image-close:before {
  content: "\e79e";
}

.icon-dashboard:before {
  content: "\e6db";
}

.icon-stamp:before {
  content: "\e71a";
}

.icon-idcard:before {
  content: "\e6d9";
}

.icon-highlight:before {
  content: "\e79f";
}

.icon-user-add:before {
  content: "\e6a5";
}

.icon-live-broadcast:before {
  content: "\e780";
}

.icon-sun-fill:before {
  content: "\e6a4";
}

.icon-wifi:before {
  content: "\e6a6";
}

.icon-qrcode:before {
  content: "\e6a7";
}

.icon-tags:before {
  content: "\e6a8";
}

.icon-woman:before {
  content: "\e6a9";
}

.icon-notification:before {
  content: "\e6a3";
}

.icon-location:before {
  content: "\e6ab";
}

.icon-user-group:before {
  content: "\e6ac";
}

.icon-schedule:before {
  content: "\e6ad";
}

.icon-thunderbolt:before {
  content: "\e6ae";
}

.icon-tool:before {
  content: "\e6af";
}

.icon-loading:before {
  content: "\e6b0";
}

.icon-phone:before {
  content: "\e6b1";
}

.icon-interaction:before {
  content: "\e6b2";
}

.icon-safe:before {
  content: "\e6b3";
}

.icon-moon-fill:before {
  content: "\e6b4";
}

.icon-Fire:before {
  content: "\e6b5";
}

.icon-moon:before {
  content: "\e6b7";
}

.icon-dice:before {
  content: "\e6a2";
}

.icon-file-image:before {
  content: "\e6b8";
}

.icon-tag:before {
  content: "\e6b9";
}

.icon-shake:before {
  content: "\e6ba";
}

.icon-loop:before {
  content: "\e6bb";
}

.icon-notification-close:before {
  content: "\e6bd";
}

.icon-ear:before {
  content: "\e6be";
}

.icon-menu:before {
  content: "\e6bf";
}

.icon-trophy:before {
  content: "\e6c0";
}

.icon-robot-add:before {
  content: "\e6c1";
}

.icon-folder:before {
  content: "\e6c2";
}

.icon-user:before {
  content: "\e6c3";
}

.icon-nav:before {
  content: "\e6c5";
}

.icon-printer:before {
  content: "\e6c6";
}

.icon-subscribe-add:before {
  content: "\e6c7";
}

.icon-robot:before {
  content: "\e6c8";
}

.icon-sun:before {
  content: "\e6c9";
}

.icon-common:before {
  content: "\e6a1";
}

.icon-video-camera:before {
  content: "\e6cb";
}

.icon-mobile:before {
  content: "\e6cc";
}

.icon-old-version:before {
  content: "\e6cd";
}

.icon-folder-delete:before {
  content: "\e6ce";
}

.icon-skin:before {
  content: "\e6cf";
}

.icon-subscribe:before {
  content: "\e6d0";
}

.icon-subscribed:before {
  content: "\e6d1";
}

.icon-mind-mapping:before {
  content: "\e6d2";
}

.icon-pen-fill:before {
  content: "\e6d3";
}

.icon-drag-dot-vertical:before {
  content: "\e6d4";
}

.icon-unlock:before {
  content: "\e6d6";
}

.icon-pushpin:before {
  content: "\e6d7";
}

.icon-public:before {
  content: "\e6d8";
}

.icon-language:before {
  content: "\e6da";
}

.icon-book:before {
  content: "\e6dc";
}

.icon-folder-add:before {
  content: "\e6dd";
}

.icon-pen:before {
  content: "\e6de";
}

.icon-drive-file:before {
  content: "\e6df";
}

.icon-man:before {
  content: "\e6e0";
}

.icon-layout:before {
  content: "\e6e2";
}

.icon-file-video:before {
  content: "\e6e4";
}

.icon-image:before {
  content: "\e6e5";
}

.icon-copyright:before {
  content: "\e6e6";
}

.icon-empty:before {
  content: "\e6e7";
}

.icon-bulb:before {
  content: "\e6e8";
}

.icon-camera:before {
  content: "\e6e9";
}

.icon-email:before {
  content: "\e6ea";
}

.icon-lock:before {
  content: "\e6eb";
}

.icon-file-pdf:before {
  content: "\e6ec";
}

.icon-drag-dot:before {
  content: "\e6ed";
}

.icon-file:before {
  content: "\e6ef";
}

.icon-apps:before {
  content: "\e6f0";
}

.icon-calendar:before {
  content: "\e6f2";
}

.icon-compass:before {
  content: "\e6f3";
}

.icon-bug:before {
  content: "\e6f4";
}

.icon-file-audio:before {
  content: "\e6f5";
}

.icon-desktop:before {
  content: "\e6f6";
}

.icon-experiment:before {
  content: "\e6f7";
}

.icon-command:before {
  content: "\e6f8";
}

.icon-cloud:before {
  content: "\e6f9";
}

.icon-application:before {
  content: "\e6fa";
}

.icon-ordered-list:before {
  content: "\e69f";
}

.icon-zoom-out:before {
  content: "\e6a0";
}

.icon-copy:before {
  content: "\e69d";
}

.icon-h5:before {
  content: "\e69e";
}

.icon-edit:before {
  content: "\e6fb";
}

.icon-link:before {
  content: "\e6fc";
}

.icon-strikethrough:before {
  content: "\e6fd";
}

.icon-zoom-in:before {
  content: "\e6fe";
}

.icon-unordered-list:before {
  content: "\e6ff";
}

.icon-paste:before {
  content: "\e700";
}

.icon-quote:before {
  content: "\e701";
}

.icon-undo:before {
  content: "\e702";
}

.icon-underline:before {
  content: "\e703";
}

.icon-scissor:before {
  content: "\e704";
}

.icon-original-size:before {
  content: "\e705";
}

.icon-line-height:before {
  content: "\e706";
}

.icon-redo:before {
  content: "\e707";
}

.icon-sort-ascending:before {
  content: "\e708";
}

.icon-sort-descending:before {
  content: "\e709";
}

.icon-font-colors:before {
  content: "\e70a";
}

.icon-sort:before {
  content: "\e70b";
}

.icon-oblique-line:before {
  content: "\e70c";
}

.icon-h7:before {
  content: "\e70d";
}

.icon-brush:before {
  content: "\e70e";
}

.icon-eraser:before {
  content: "\e70f";
}

.icon-h6:before {
  content: "\e710";
}

.icon-italic:before {
  content: "\e711";
}

.icon-h3:before {
  content: "\e712";
}

.icon-delete:before {
  content: "\e713";
}

.icon-h1:before {
  content: "\e714";
}

.icon-find-replace:before {
  content: "\e715";
}

.icon-filter:before {
  content: "\e716";
}

.icon-formula:before {
  content: "\e717";
}

.icon-align-left:before {
  content: "\e718";
}

.icon-circular:before {
  content: "\e719";
}

.icon-h4:before {
  content: "\e71b";
}

.icon-h2:before {
  content: "\e71c";
}

.icon-bold:before {
  content: "\e71d";
}

.icon-align-right:before {
  content: "\e71e";
}

.icon-bg-colors:before {
  content: "\e71f";
}

.icon-align-center:before {
  content: "\e720";
}

.icon-rotate-left:before {
  content: "\e69b";
}

.icon-to-top:before {
  content: "\e69c";
}

.icon-double-left:before {
  content: "\e69a";
}

.icon-swap:before {
  content: "\e721";
}

.icon-up-circle:before {
  content: "\e722";
}

.icon-to-bottom:before {
  content: "\e723";
}

.icon-up:before {
  content: "\e724";
}

.icon-rotate-right:before {
  content: "\e725";
}

.icon-menu-fold:before {
  content: "\e726";
}

.icon-menu-unfold:before {
  content: "\e727";
}

.icon-right-circle:before {
  content: "\e728";
}

.icon-left:before {
  content: "\e729";
}

.icon-left-circle:before {
  content: "\e72a";
}

.icon-right:before {
  content: "\e72b";
}

.icon-down:before {
  content: "\e72c";
}

.icon-shrink:before {
  content: "\e72d";
}

.icon-to-left:before {
  content: "\e72e";
}

.icon-drag-arrow:before {
  content: "\e72f";
}

.icon-caret-up:before {
  content: "\e730";
}

.icon-to-right:before {
  content: "\e731";
}

.icon-double-right:before {
  content: "\e732";
}

.icon-caret-down:before {
  content: "\e733";
}

.icon-expand:before {
  content: "\e734";
}

.icon-caret-right:before {
  content: "\e735";
}

.icon-double-down:before {
  content: "\e736";
}

.icon-arrow-up:before {
  content: "\e737";
}

.icon-arrow-rise:before {
  content: "\e738";
}

.icon-arrow-left:before {
  content: "\e739";
}

.icon-double-up:before {
  content: "\e73a";
}

.icon-arrow-down:before {
  content: "\e73b";
}

.icon-caret-left:before {
  content: "\e73c";
}

.icon-down-circle:before {
  content: "\e73d";
}

.icon-arrow-fall:before {
  content: "\e73e";
}

.icon-arrow-right:before {
  content: "\e73f";
}

.icon-message-banned:before {
  content: "\e740";
}

.icon-voice:before {
  content: "\e741";
}

.icon-thumb-down:before {
  content: "\e742";
}

.icon-translate:before {
  content: "\e743";
}

.icon-thumb-up-fill:before {
  content: "\e744";
}

.icon-search:before {
  content: "\e746";
}

.icon-share-internal:before {
  content: "\e747";
}

.icon-star-fill:before {
  content: "\e748";
}

.icon-thumb-down-fill:before {
  content: "\e749";
}

.icon-upload:before {
  content: "\e74a";
}

.icon-settings:before {
  content: "\e74b";
}

.icon-star:before {
  content: "\e74c";
}

.icon-sync:before {
  content: "\e74d";
}

.icon-share-alt:before {
  content: "\e74e";
}

.icon-thumb-up:before {
  content: "\e74f";
}

.icon-message:before {
  content: "\e751";
}

.icon-refresh:before {
  content: "\e752";
}

.icon-select-all:before {
  content: "\e754";
}

.icon-reply:before {
  content: "\e755";
}

.icon-list:before {
  content: "\e756";
}

.icon-import:before {
  content: "\e758";
}

.icon-download:before {
  content: "\e759";
}

.icon-cloud-download:before {
  content: "\e75a";
}

.icon-poweroff:before {
  content: "\e75b";
}

.icon-share-external:before {
  content: "\e75c";
}

