import i18n from '@/locales';
import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'


const instance = axios.create({
  baseURL: import.meta.env.VITE_APP_API,
  withCredentials: false,
  timeout:300000
})


// 请求拦截器
instance.interceptors.request.use(
  config => {
    config = {
      neddLogin: true,
      ...config,
    }
    if (config.neddLogin){
      config.headers.Authorization = localStorage.token ? localStorage.token : 'TOKEN visitor'
      config.headers['PLANTCODE'] = localStorage.plant_code ? localStorage.plant_code : ''
      // config.headers['MAC'] = macAddress
      config.headers['MAC'] = localStorage.mac ? localStorage.mac : ''
    } 
    let currentLan= localStorage.duixian_lang
    if(currentLan && currentLan == 'en'){
      // config.headers['language']= 'en'
    }

    
    return config
  },
  error => {
    ElMessage.error(i18n.global.t('utils.http.sskd2q'))
    Promise.reject(error)
  }
)

// 相应拦截器
instance.interceptors.response.use(
  response => {
    const { data, status } = response

    if (status && status == 200) {
      if ([401, 403].includes(data.code)) {
        ElMessage.warning(i18n.global.t('utils.http.kjfq8h'))
        const userStore = useUserStore()
        userStore.logout()
        return Promise.reject(data)
      } else if (data.code != 200) {
        ElMessage.error(data.msg)
        return Promise.reject(data)
      }
      if(data.token){
        localStorage.token = 'TOKEN ' + data.token
      }
      return data.data
    } else {
      ElMessage.error(data.msg)
      return Promise.reject(data)
    }
    
    
  },
  error => {
    if ([401, 403].includes(error?.response?.status)) {
      ElMessage.warning(i18n.global.t('utils.http.kjfq8h'))
      const userStore = useUserStore()
      userStore.logout()
      return
    }

    ElMessage.error(i18n.global.t('utils.http.sskd2q'))
    return Promise.reject(error)
  }
)

export const http = instance
export default instance
