<template>
  <div class="flex items-center" :class="className">
    <BaseIcon
      :class="iconClass"
      icon="a-Multi-chainUnique"
      @click="uniqueClick"
    />
    <BaseIcon :class="iconClass" icon="a-Multi-chainCopy" @click="copyClick" />
  </div>
</template>
<script setup>
import BaseIcon from "@/components/BaseIcon/index.vue";
defineProps({
  iconClass: {
    type: String,
    default: "text-[16px]",
  },
  className: {
    type: String,
    default: "gap-[3px]",
  },
});
const emit = defineEmits(["uniqueClick", "copyClick"]);
function uniqueClick() {
  console.log("uniqueClick");
  emit("uniqueClick");
}
function copyClick() {
  console.log("copyClick");
  emit("copyClick");
}
</script>
<style lang="scss"></style>
