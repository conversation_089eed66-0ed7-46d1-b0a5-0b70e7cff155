<template>
  <div id="app">
    <el-config-provider :locale="locales">
      <RouterView />
      <!-- 主内容区域 -->

      <!-- 全局消息通知组件 -->
    </el-config-provider>
  </div>
</template>

<script setup>
import zhCn from "element-plus/es/locale/lang/zh-cn.mjs";
import en from "element-plus/es/locale/lang/en-mjs";
import { useUserStore } from "@/stores/user";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();

const userStore = useUserStore();

const lang = computed(() => userStore.lang);
const locales = computed(() => {
  return lang.value === "en" ? en : zhCn;
});

watch(
  () => lang.value,
  (data) => {
    if (data) {
      locale.value = data;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style scoped lang="scss">
@font-face {
  font-family: "SF PRO";
  src: url("@/libs/font/SFPRODISPLAYREGULAR.OTF");
  font-weight: normal;
  font-style: normal;
}
*,
body {
  font-family: "SF PRO", PingFangSC, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}
</style>
