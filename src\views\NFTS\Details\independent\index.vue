<template>
  <div id="common-nfts-detail-content">
    <div class="details-box">
      <div class="basic-box">
        <div class="left-box border w-[656px]">
          <div>
            <Image
              :url="imgUrl"
              :enlarge="true"
              className="w-[576px] h-[576px] rounded-[16px] block mx-auto"
            />
          </div>
          <AnalysisInfo class="mt-[37px]" />
        </div>
        <div class="right-box border w-[656px]">
          <BasicInfo />
        </div>
      </div>

      <div class="more-box border">
        <MoreCollection />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import Image from "@/views/NFTS/Details/components/Image.vue";
import imgUrl from "@/assets/NFTS/details/detail_1.png";
import AnalysisInfo from "../components/AnalysisInfo/index.vue";
import BasicInfo from "../components/BasicInfo/index.vue";
import MoreCollection from "../components/MoreCollection/index.vue";
</script>
<style lang="scss" scoped>
.border {
  border: 1px red solid;
}
</style>
