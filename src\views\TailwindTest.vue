<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
    <BaseIcon class="text-[20px]" icon="shopping_cart" color="#ff0000" />
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Tailwind CSS 测试页面</h1>
        <p class="text-lg text-gray-600">如果你能看到这些样式，说明 Tailwind CSS 已经成功工作！</p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 卡片 1 -->
        <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
          <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
            <span class="text-white text-xl font-bold">1</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">响应式设计</h3>
          <p class="text-gray-600">使用 md: 和 lg: 前缀来创建响应式布局</p>
        </div>

        <!-- 卡片 2 -->
        <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
          <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4">
            <span class="text-white text-xl font-bold">2</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">颜色系统</h3>
          <p class="text-gray-600">丰富的颜色类如 bg-blue-500, text-green-600</p>
        </div>

        <!-- 卡片 3 -->
        <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
          <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4">
            <span class="text-white text-xl font-bold">3</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">间距系统</h3>
          <p class="text-gray-600">统一的间距类如 p-6, m-4, space-x-4</p>
        </div>
      </div>

      <div class="mt-12 text-center">
        <button
          class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 mr-4">
          主要按钮
        </button>
        <button
          class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
          次要按钮
        </button>
      </div>

      <div class="mt-8 p-6 bg-yellow-50 border-l-4 border-yellow-400 rounded">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">
              这是一个提示框，展示了 Tailwind CSS 的边框、背景色和图标样式。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import BaseIcon from "@/components/BaseIcon/index.vue"

</script>
