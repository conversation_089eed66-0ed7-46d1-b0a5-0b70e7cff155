import router from './router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const hasToken = localStorage.token

  next()
  // if (to.path !== '/login') {
  //   if (userStore.token) {
  //     next()
  //   } else {
  //     next('/login')
  //   }
  // } else {
  //   next()
  // }
})

