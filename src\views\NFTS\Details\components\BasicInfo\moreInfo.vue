<template>
  <div class="w-full" :class="className">
    <div class="flex items-center gap-[24px] text-[14px]">
      <div class="flex items-center">
        <span>By:The Doge Capital</span>
        <BaseIcon class="ml-[5px]" icon="a-Multi-chainUnique" />
      </div>
      <span>2023.06.12</span>
      <span>Art</span>
    </div>
    <p class="mt-[8px] text-justify">
      5000 Doges Offering Solana On Chain Services - Web3 Software Development -
      Blockchain Education
    </p>
  </div>
</template>
<script setup>
import BaseIcon from "@/components/BaseIcon/index.vue";

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  className: {
    type: String,
    default: "",
  },
});
</script>
