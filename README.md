# NFT_Web
NFT_Web端

## css变量在 src/style/index.scss内
## 通用的css在 src/style/common.scss内
## 所有颜色，统一抽取css变量（后期会做深浅主题切换）

## 路由只是简单定义了下，每个功能模块对应路由可以自行修改
## 现有页面都可以自行修改

## 每个页面做完后，请抽取下多语言，存放文件在 src/locales/zh-CN.json
1. 可以在vscode安装i18n-tools，直接当前页面整体提取(适用于中文)，英文用单个抽取(i18n ally 或 vue-i18n)
2. 抽取格式：全部扁平化，例如用户中心-仪表盘-xxx(userinfo.dashboard.xxx)


## 页面功能模块
1. api: 接口
2. assets: 静态资源
3. components: 组件
4. locales: 多语言
5. router: 路由
6. stores: 状态管理（pinia）
7. utils: 公用方法
8. views: 页面
9. guide: 通用的组件、样式、插件等文档说明，统一写在对应md文档内