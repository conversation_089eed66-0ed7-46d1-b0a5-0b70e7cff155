<template>
    <div>
        <a-radio-group v-model="date" type="button">
            <a-radio value="All time">All time</a-radio>
            <a-radio value="1 day">1 day</a-radio>
            <a-radio value="7 days">7 days</a-radio>
            <a-radio value="30 days">30 days</a-radio>
        </a-radio-group>
        <BaseCharts v-model="chartsOptions" height="300px" />
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import BaseCharts from '@/components/BaseCharts/index.vue'

const date = ref('All time')

const getData = () => {
    return Array(11).fill().map((_, index) => (Math.random() * 0.06).toFixed(2))
}

const chartsData1 = ref(getData())
const chartsData2 = ref(getData())
const chartsData3 = ref(getData())

const colors = ['#0054FA', '#00DE73', '#627EEA']

const chartsOptions = ref(
    {
        grid: {
            // left: '2%',
            // right: '4%',
            // bottom: '10%',
            // top: '3%'
            containLabel: true
        },
        legend: {
            show: true,
            itemWidth: 12,
            itemHeight: 12,
            bottom: '5%',
        },
        xAxis: {
            type: 'category',
            data: ['Jul 14', 'Jul 26', 'Aug 7', 'Aug 12', 'Aug 13', 'Aug 13', 'Aug 13', 'Aug 12', 'Aug 13', 'Aug 13', 'Aug 13'],
            axisTick: {
                show: false
            },
            axisLabel: {
                textStyle: {
                    color: '#8C8C8C'
                }
            },
            axisLine: {
                lineStyle: {
                    color: '#8C8C8C'
                }
            }
        },
        yAxis: {
            type: 'value',
            splitLine: {
                show: false,
            },
            axisLabel: {
                textStyle: {
                    color: '#8C8C8C'
                }
            },
            axisLine: {
                lineStyle: {
                    color: '#8C8C8C'
                }
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'line',
                z: 0, // 层级（权重）
                triggerOn: 'click',
                lineStyle: {
                    type: 'solid', // 将虚线改为实线
                    // width: 80, // 设置背景的宽度
                    cap: 'square',
                    opacity: 1,
                    color: 'white', // 设置背景颜色为白色
                    shadowBlur: 10, // 阴影模糊度
                    shadowColor: 'rgba(0, 0, 0, 0.1)', // 阴影颜色
                    shadowOffsetX: 0, // 水平方向阴影偏移距离
                    shadowOffsetY: 5
                }
            },

        },
        series: [
            {
                name: 'Ethereum',
                data: chartsData1.value,
                type: 'line',
                smooth: true,
                symbolSize: 6,
                lineStyle: {
                    width: 1,
                    color: colors[0]
                },
            },
            {
                name: 'Abstract',
                data: chartsData2.value,
                type: 'line',
                smooth: true,
                symbolSize: 6,
                lineStyle: {
                    width: 1,
                    color: colors[1]
                },
            },
            {
                name: 'ApeChain',
                data: chartsData3.value,
                type: 'line',
                smooth: true,
                symbolSize: 6,
                lineStyle: {
                    width: 1,
                    color: colors[2]
                },
            }
            // {
            //     name: 'Abstract',
            //     data: chartsData2.value,
            //     type: 'line',
            //     smooth: true,
            //     symbolSize: 10,
            //     symbol: 'circle',
            //     lineStyle: {
            //         width: 3,
            //         color: '#7AD2DE'
            //     },
            //     itemStyle: {
            //         // 设置数据点（圆点）的颜色
            //         color: '#7AD2DE', // 可以是具体的颜色值，如 'blue'、'#123456' 等
            //         borderColor: '#fff', // 数据点边框颜色
            //         borderWidth: 2 // 数据点边框宽度
            //     }
            // },
            // {
            //     name: 'ApeChain',
            //     data: chartsData3.value,
            //     type: 'line',
            //     smooth: true,
            //     symbolSize: 10,
            //     symbol: 'circle',
            //     lineStyle: {
            //         width: 3,
            //         color: '#4080FF'
            //     },
            //     itemStyle: {
            //         color: '#4080FF',
            //         borderColor: '#fff',
            //         borderWidth: 2
            //     }
            // },
        ]
    }
)

</script>
<style lang="scss" scoped></style>