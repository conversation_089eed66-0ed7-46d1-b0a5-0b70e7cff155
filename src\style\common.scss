#common-nfts-detail-content {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  .basic-box {
    display: grid;
    grid-template-columns: auto auto;
    gap: 64px;
  }

  .more-box {
    margin-top: 48px;
  }
  .overflow-one-hidden {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .info-outline-button {
    border-color: #e6e6e6;
    color: #000000;
    padding: 0 12px;
  }
  .arco-btn-info {
    background-color: #f2f2f2;
  }
  .arco-table-td {
    border-bottom-width: 0px;
  }
  .arco-table-th {
    background-color: transparent;
    border-bottom-width: 1px;
    color: rgba(89, 89, 89, 0.65);
    font-size: 12px;
    line-height: 140%;
  }

  .arco-radio-group-button {
    border-radius: 16px;
    .arco-radio-checked {
      border-radius: 20px;
      color: #000;
      font-weight: 600;
    }
    .arco-radio-button:hover {
      border-radius: 20px;
    }
  }
  .coin-tabs {
    .arco-descriptions-item-label {
      color: #8c8c8c;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      padding-right: 60px;
      &:last-child {
        padding-right: 0;
      }
    }
    .arco-card-body{
      padding: 24px;
    }
    .arco-descriptions-item-value {
      color: #000;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      padding-right: 60px;
      &:last-child {
        padding-right: 0;
      }
    }
  }
}
